/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-blue: #1e3a8a;
    --secondary-blue: #3b82f6;
    --light-blue: #dbeafe;
    --primary-gold: #f59e0b;
    --secondary-gold: #fbbf24;
    --light-gold: #fef3c7;
    --primary-beige: #f5f5dc;
    --secondary-beige: #faf5e6;
    --white: #ffffff;
    --dark-text: #1f2937;
    --light-text: #6b7280;
    --shadow: rgba(0, 0, 0, 0.1);
    
    /* Typography */
    --font-primary: 'Open Sans', sans-serif;
    --font-heading: 'Playfair Display', serif;
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 5rem 0;
    --element-spacing: 2rem;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--dark-text);
    background-color: var(--white);
    overflow-x: hidden;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    color: var(--primary-blue);
    text-align: center;
    margin-bottom: 0.5rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--secondary-gold));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--light-text);
    text-align: center;
    margin-top:20px;
    margin-bottom: 3rem;
}

.lead {
    font-size: 1.2rem;
    font-weight: 400;
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 20px var(--shadow);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-logo:hover {
    color: var(--primary-gold);
}

.rotating-sun {
    color: var(--primary-gold);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-text);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gold);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-blue);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--secondary-beige) 0%, var(--light-blue) 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        rgba(30, 58, 138, 0.1) 0%, 
        rgba(245, 158, 11, 0.1) 50%, 
        rgba(59, 130, 246, 0.1) 100%);
    animation: sunrise 3s ease-out;
}

@keyframes sunrise {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease-out 0.5s both;
}

.title-main {
    display: block;
    color: var(--primary-blue);
    font-weight: 700;
}

.title-sub {
    display: block;
    color: var(--primary-gold);
    font-weight: 400;
    font-size: 0.8em;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--light-text);
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease-out 0.7s both;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--dark-text);
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    animation: fadeInUp 1s ease-out 1.1s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--secondary-blue);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

/* Hero Showcase */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out 0.5s both;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-showcase {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Featured Project Showcase */
.showcase-featured {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.featured-project {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transition: all 0.4s ease;
    cursor: pointer;
    height: 350px;
}

.featured-project:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
}

.featured-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.4s ease;
}

.featured-project:hover .featured-img {
    transform: scale(1.05);
}

.featured-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 30px;
    transform: translateY(100%);
    transition: transform 0.4s ease;
}

.featured-project:hover .featured-overlay {
    transform: translateY(0);
}

.featured-content {
    text-align: left;
}

.featured-label {
    display: inline-block;
    background: var(--primary-gold);
    color: var(--dark-blue);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.featured-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.featured-content p {
    font-size: 1rem;
    line-height: 1.5;
    margin: 0 0 20px 0;
    opacity: 0.95;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.featured-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--primary-gold);
    color: var(--dark-blue);
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.featured-btn:hover {
    background: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}



/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

.scroll-arrow {
    color: var(--primary-gold);
    font-size: 1.5rem;
}

/* Section Styles */
section {
    padding: var(--section-padding);
}

.section-header {
    margin-bottom: 4rem;
}

/* About Section */
.about {
    background: linear-gradient(135deg, var(--primary-beige), var(--secondary-beige));
    padding: 100px 0;
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.about-text {
    text-align: left;
}

.about-text .lead {
    font-size: 1.3rem;
    font-weight: 300;
    color: var(--primary-blue);
    margin-bottom: 30px;
    line-height: 1.6;
}

.about-text p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* About Story Section - Full Width */
.about-story {
    margin-top: 4rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.story-title {
    color: var(--primary-blue);
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.story-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    border-radius: 2px;
}

.about-detailed {
    margin: 2rem 0;
    padding: 3rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border-left: 5px solid var(--primary-gold);
    box-shadow: 0 10px 30px var(--shadow);
    backdrop-filter: blur(10px);
}

.about-detailed p {
    line-height: 1.7;
    margin-bottom: 1.5rem;
    text-align: justify;
    font-size: 1.05rem;
}

.about-detailed p:last-child {
    margin-bottom: 0;
}

.fun-fact {
    background: linear-gradient(135deg, var(--light-blue), rgba(212, 175, 55, 0.1));
    padding: 2rem;
    border-radius: 15px;
    margin-top: 2rem;
    border-left: 5px solid var(--secondary-gold);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.fun-fact p {
    margin: 0;
    font-style: italic;
    color: var(--primary-blue);
    font-size: 1.05rem;
}

/* Centered feature cards container */
.about-features-centered {
    max-width: 1000px;
    margin: 60px auto 0;
    padding: 0 20px;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.feature {
    background: rgba(255, 255, 255, 0.8);
    padding: 25px 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    text-align: center;
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px var(--shadow);
}

.feature i {
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: 15px;
}

.feature h3 {
    color: var(--primary-blue);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.feature p {
    color: var(--text-dark);
    line-height: 1.5;
    font-size: 0.95rem;
}

/* About Photo Section */
.about-photo {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 20px;
}

.photo-container {
    position: relative;
    max-width: 3500px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 40px var(--shadow);
    transition: all 0.3s ease;
}

.photo-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px var(--shadow);
}

.profile-photo {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.3s ease;
}

.photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(30, 58, 138, 0.9));
    padding: 30px 20px 20px;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.photo-container:hover .photo-overlay {
    transform: translateY(0);
}

.photo-caption {
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* Responsive adjustments for about section */
@media (max-width: 992px) {
    .about-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .about-features-centered {
        margin-top: 50px;
    }
}

@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .about-text {
        text-align: center;
    }

    .about-features {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .about-features-centered {
        margin-top: 40px;
        padding: 0 10px;
    }

    .showcase-featured {
        max-width: 100%;
        padding: 0 10px;
    }

    .featured-project {
        height: 280px;
    }

    .featured-overlay {
        padding: 20px;
    }

    .featured-content h3 {
        font-size: 1.5rem;
    }

    .featured-content p {
        font-size: 0.9rem;
    }
}

/* Process Section */
.process {
    background: linear-gradient(135deg, var(--light-blue), var(--secondary-beige));
}

.process-intro {
    max-width: 800px;
    margin: 0 auto 3rem auto;
    text-align: center;
}

.process-intro .lead {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.process-intro p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

.process-steps {
    display: grid;
    gap: 2.5rem;
    max-width: 900px;
    margin: 0 auto;
}

/* Expandable Step Styles */
.expandable-step {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 5px 20px var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.expandable-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px var(--shadow);
}

.step-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.step-header:hover {
    background-color: #ffffff;
}

.step-number {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
}

.step-basic {
    flex: 1;
}

.step-basic h3 {
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.step-summary {
    color: var(--text-gray);
    margin: 0;
    font-size: 1rem;
    line-height: 1.5;
}

.step-toggle {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--light-blue);
    color: var(--primary-blue);
    transition: all 0.3s ease;
}

.step-toggle i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.expandable-step.expanded .step-toggle {
    background-color: var(--primary-blue);
    color: var(--white);
}

.expandable-step.expanded .step-toggle i {
    transform: rotate(180deg);
}

.step-details {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                padding 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.2s ease;
    padding: 0 2rem;
    opacity: 0;
}

.expandable-step.expanded .step-details {
    max-height: 800px;
    padding: 0 2rem 2rem 2rem;
    opacity: 1;
}

.step-details p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-gray);
}

.step-details p:last-child {
    margin-bottom: 0;
}

/* Gallery Section */
.gallery {
    background: var(--white);
}

.gallery-nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.gallery-nav-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--primary-blue);
    background: transparent;
    color: var(--primary-blue);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.gallery-nav-btn:hover,
.gallery-nav-btn.active {
    background: var(--primary-blue);
    color: var(--white);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.gallery-item {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px var(--shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px var(--shadow);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.gallery-item-content {
    padding: 1.5rem;
}

.gallery-item h3 {
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.gallery-item p {
    color: var(--light-text);
    font-size: 0.9rem;
}

/* Education Section */
.education {
    background: white;
}

.education-intro {
    max-width: 900px;
    margin: 0 auto 4rem auto;
    text-align: center;
}

.education-intro .lead {
    font-size: 1.2rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: var(--primary-blue);
}

.education-intro p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-gray);
}

.education-section {
    margin-bottom: 4rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.section-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 2rem;
}

.section-content.reverse {
    direction: rtl;
}

.section-content.reverse > * {
    direction: ltr;
}

.content-text h3 {
    color: var(--primary-blue);
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.content-text h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
    border-radius: 2px;
}

.content-text p {
    line-height: 1.7;
    margin-bottom: 1.2rem;
    color: var(--text-gray);
    text-align: justify;
}

.content-image {
    text-align: center;
}

.education-img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow);
    transition: transform 0.3s ease;
}

.education-img:hover {
    transform: scale(1.05);
}

.image-caption {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--text-gray);
    font-style: italic;
    line-height: 1.4;
}

/* Ťuk and Bzuk Fun Section */
.fun-section {
    background: linear-gradient(135deg, var(--light-blue), rgba(212, 175, 55, 0.1));
    padding: 3rem;
    border-radius: 20px;
    margin-top: 4rem;
    box-shadow: 0 15px 40px var(--shadow);
}

.fun-section .section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.fun-section h3 {
    color: var(--primary-blue);
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.fun-section .section-subtitle {
    color: var(--text-gray);
    font-size: 1.1rem;
    font-style: italic;
}

.tuk-bzuk-content p {
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: var(--text-gray);
    text-align: justify;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.tuk-bzuk-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.tuk-bzuk-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 25px var(--shadow);
    transition: transform 0.3s ease;
    text-align: center;
}

.tuk-bzuk-item:hover {
    transform: translateY(-5px);
}

.tuk-bzuk-img,
.tuk-bzuk-video {
    width: 100%;
    height: auto;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.video-item {
    grid-column: 1 / -1;
    max-width: 600px;
    margin: 0 auto;
    justify-self: center;
}

.conclusion {
    text-align: center;
    font-style: italic;
    color: var(--primary-blue);
    font-size: 1.1rem;
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Contact Section */
.contact {
    background: var(--primary-blue);
    color: var(--white);
}

.contact .section-title {
    color: var(--white);
}

.contact .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 800px;
    margin: 0 auto;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-gold);
    width: 30px;
}

.contact-item h3 {
    margin-bottom: 0.5rem;
    color: var(--white);
}

.contact-item a {
    color: var(--primary-gold);
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

.social-media h3 {
    color: var(--white);
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    text-decoration: none;
    color: var(--white);
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.5rem;
}

.facebook:hover {
    background: #1877f2;
}

.instagram:hover {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-beige), var(--secondary-beige));
    padding: 120px 0 80px;
    text-align: center;
}

.page-header-content {
    max-width: 800px;
    margin: 0 auto;
}

.page-title {
    font-family: var(--font-heading);
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-weight: 700;
}

.page-subtitle {
    font-size: 1.3rem;
    color: var(--text-dark);
    opacity: 0.8;
    font-weight: 300;
}

/* Contact Footer */
.contact-footer {
    background: var(--primary-blue);
    color: white;
    padding: 40px 0 20px;
}

.contact-footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 20px;
}

.contact-footer .contact-info h3 {
    color: var(--primary-gold);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.contact-footer .contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.contact-footer .contact-item i {
    color: var(--primary-gold);
    font-size: 1.1rem;
    width: 20px;
}

.contact-footer .contact-item a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-footer .contact-item a:hover {
    color: var(--primary-gold);
}

.contact-footer .social-links {
    display: flex;
    gap: 15px;
}

.contact-footer .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.contact-footer .social-link:hover {
    background: var(--primary-gold);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Footer */
.footer {
    background: var(--dark-text);
    color: var(--white);
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: var(--font-heading);
    font-size: 1.2rem;
    font-weight: 600;
}

.footer-logo i {
    color: var(--primary-gold);
}

.footer-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    max-width: 90%;
    max-height: 90%;
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: var(--white);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 2001;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    display: flex;
    height: 80vh;
}

.modal-body img {
    flex: 1;
    object-fit: cover;
}

.modal-info {
    flex: 0 0 300px;
    padding: 2rem;
    background: var(--white);
    overflow-y: auto;
}

.modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 2rem;
    pointer-events: none;
}

.modal-nav-btn {
    background: rgba(0, 0, 0, 0.5);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    pointer-events: all;
    transition: background 0.3s ease;
}

.modal-nav-btn:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* Gallery Item Meta Styles */
.gallery-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--light-blue);
    font-size: 0.85rem;
}

.gallery-item-meta .year {
    background: var(--primary-gold);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.gallery-item-meta .location {
    color: var(--light-text);
    font-style: italic;
}

/* Modal Meta Styles */
.modal-meta {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--light-blue);
}

.modal-meta p {
    margin-bottom: 0.5rem;
    color: var(--dark-text);
}

.modal-meta strong {
    color: var(--primary-blue);
}

/* Light Reflection Effect on Hero Title */
.hero-title {
    position: relative;
    overflow: hidden;
}

.hero-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(245, 158, 11, 0.3),
        transparent);
    transition: left 0.6s ease;
}

.hero-title:hover::before {
    left: 100%;
}

/* Enhanced Scroll Animations */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Enhanced Button Hover Effects */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

/* Improved Gallery Hover Effects */
.gallery-item {
    position: relative;
    overflow: hidden;
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(30, 58, 138, 0.1),
        rgba(245, 158, 11, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.gallery-item:hover::before {
    opacity: 1;
}

.gallery-item-content {
    position: relative;
    z-index: 2;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        gap: 3rem;
    }

    .sundial-illustration {
        width: 250px;
        height: 250px;
    }

    .sundial-base {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 768px) {
    :root {
        --section-padding: 3rem 0;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        box-shadow: 0 5px 20px var(--shadow);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .showcase-grid {
        grid-template-columns: 1fr;
        grid-template-rows: 2fr 1fr 1fr 1fr;
        max-width: 300px;
        height: 500px;
        gap: 10px;
    }

    .showcase-main {
        grid-row: 1 / 2;
    }

    .showcase-overlay {
        padding: 15px 10px 10px;
    }

    .showcase-overlay h4 {
        font-size: 0.9rem;
    }

    .showcase-overlay p {
        font-size: 0.75rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .page-header {
        padding: 100px 0 60px;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
    }

    .contact-footer-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .contact-footer .social-links {
        justify-content: center;
    }

    .modal-body {
        flex-direction: column;
        height: auto;
    }

    .modal-info {
        flex: none;
    }

    .step {
        flex-direction: column;
        text-align: center;
    }

    .sundial-illustration {
        width: 200px;
        height: 200px;
    }

    .sundial-base {
        width: 120px;
        height: 120px;
    }

    .time-markers {
        width: 100px;
        height: 100px;
    }

    .marker {
        font-size: 0.7rem;
        transform: translateX(-50%) rotate(var(--angle)) translateY(-40px) rotate(calc(-1 * var(--angle)));
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .about-features,
    .education-models {
        grid-template-columns: 1fr;
    }

    .section-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .section-content.reverse {
        direction: ltr;
    }

    .content-text h3 {
        font-size: 1.5rem;
        text-align: center;
    }

    .content-text h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .content-text p {
        text-align: left;
    }

    .education-intro .lead {
        font-size: 1.1rem;
    }

    .fun-section {
        padding: 2rem 1rem;
        margin-top: 3rem;
    }

    .fun-section h3 {
        font-size: 1.6rem;
    }

    .tuk-bzuk-gallery {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .video-item {
        grid-column: 1 / -1;
        justify-self: center;
    }

    .conclusion {
        font-size: 1rem;
        padding: 1rem;
    }

    .about-story {
        margin-top: 3rem;
        padding: 0 1rem;
    }

    .story-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .about-detailed {
        margin: 1.5rem 0;
        padding: 1.5rem;
        text-align: left;
    }

    .about-detailed p {
        text-align: left;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .fun-fact {
        padding: 1.5rem;
        margin-top: 1.5rem;
    }

    .fun-fact p {
        font-size: 1rem;
    }

    .process-steps {
        gap: 1.5rem;
    }

    .step-header {
        padding: 1.5rem;
        gap: 1rem;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .step-basic h3 {
        font-size: 1.1rem;
    }

    .step-summary {
        font-size: 0.9rem;
    }

    .step-toggle {
        width: 35px;
        height: 35px;
    }

    .step-toggle i {
        font-size: 1rem;
    }

    .expandable-step.expanded .step-details {
        padding: 0 1.5rem 1.5rem 1.5rem;
    }

    .process-intro .lead {
        font-size: 1rem;
    }
}
